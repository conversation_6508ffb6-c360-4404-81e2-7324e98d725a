/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.business.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 生产主题配置类
 * 读取application.yml中的jmq.topic.producer属性文件
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@ConfigurationProperties(prefix = "jmq.topic.producer")
@Data
public class ProducerTopicProperties {
    /**
     * pnc消息
     */
    private String serverGuardianPnc;

    /**
     * 车辆状态变化
     */
    private String serverVehicleChangeStatus;

    /**
     * Guardian报警
     */
    private String serverGuardianAbnormal;

    /**
     * Guardian电量
     */
    private String serverGuardianPower;

    /**
     * Guardian实时消息
     */
    private String serverGuardianRealtime;

    /**
     * ota
     */
    private String serverGuardianOtaVersion;

    /**
     * report异常
     */
    private String serverReportAbnormal;

    /**
     * report告警
     */
    private String serverGuardianAlarm;

    /**
     * 启动消息
     */
    private String serverReportBoot;

    /**
     * report驾驶模式
     */
    private String serverReportDriveMode;

    /**
     * report的routing
     */
    private String serverReportRoutingStatus;

    /**
     * report的硬件上报事件
     */
    private String serverReportHardwareEvent;

    /**
     * report的routing全部
     */
    private String serverReportRoutingAll;

    /**
     * bus链路obu
     */
    private String serverBusObu;

    /**
     * localView链路
     */
    private String serverDecreaseLocalView;

    /**
     * 硬件基本信息
     */
    private String serverHardwareInfo;

    /**
     * 硬件状态信息
     */
    private String serverHardwareStatus;

    /**
     * 硬件sim卡实时数据
     */
    private String serverHardwareSimData;

    /**
     * 硬件gnss实时数据
     */
    private String serverHardwareGnssData;

    /**
     * 硬件域控实时数据
     */
    private String serverHardwareDomainData;

    /**
     * 硬件底盘实时数据
     */
    private String serverHardwareChassisData;

    /**
     * 硬件连通性实时数据
     */
    private String serverHardwareConnectivity;

    /**
     * mqtt发送字节数组
     */
    private String mqttSendByte;
}