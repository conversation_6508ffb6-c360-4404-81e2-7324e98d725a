package com.jdx.rover.server.business.manager;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleRealtimeInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jodah.expiringmap.ExpirationListener;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 车辆定位可信度
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VehicleLocalizationManager {

    /**
     * ReportAlarmRepository
     */
    private final ReportAlarmRepository reportAlarmRepository;

    /**
     * VehicleAlarmManager
     */
    private final VehicleAlarmManager vehicleAlarmManager;

    /**
     * VehicleRealtimeInfoRepository
     */
    private final VehicleRealtimeInfoRepository vehicleRealtimeInfoRepository;

    /**
     * VehiclePncRepository
     */
    private final VehiclePncRepository vehiclePncRepository;

    /**
     * sceneSignalMap
     */
    private final ExpiringMap<String, ChangeStatusDTO> sceneSignalMap = ExpiringMap.builder().maxSize(500)
            // 允许 Map 元素具有各自的到期时间，并允许更改到期时间。
            .variableExpiration()
            // 设置过期时间，如果key不设置过期时间，key永久有效。
            .asyncExpirationListener(new ExpirationDataListener())
            // 设置 Map的过期策略
            .expirationPolicy(ExpirationPolicy.CREATED)
            .build();

    /**
     * 缓存车辆当前定位置信度
     */
    private final Map<String, Double> vehicleSceneSignalMap = Maps.newConcurrentMap();

    /**
     * 上报异常行驶距离阈值，单位：米
     */
    private final static double DISTANCE_THRESHOLD = 100.0;

    /**
     * 车辆连续10s定位置信度范围∈[0,3.5] 告警
     * 车辆连续5s定位置信度范围∈（3.5,5.0] 告警消失
     */
    public void handleSceneSignalAlarm(ChangeStatusDTO changeStatus) {
        if (Objects.isNull(changeStatus) || StringUtils.isBlank(changeStatus.getVehicleName()) || StringUtils.isBlank(changeStatus.getNewValue())) {
            log.info("定位置信度数据上报异常{}", JsonUtils.writeValueAsString(changeStatus));
            return;
        }
        if (Objects.isNull(changeStatus.getRecordTime()) || DateUtil.between(changeStatus.getRecordTime(), new Date(), DateUnit.MINUTE) > 10) {
            log.info("定位置信度数据日期异常{}", JsonUtils.writeValueAsString(changeStatus));
            return;
        }

        String vehicleName = changeStatus.getVehicleName();
        double sceneSignal = BigDecimal.valueOf(Double.parseDouble(changeStatus.getNewValue())).setScale(2, RoundingMode.HALF_UP).doubleValue();
        if (sceneSignal >= 0 && sceneSignal <= 3.5) {
            // 车辆有Planning且行驶距离小于100米，连续5s定位置信度小于3.5，生成告警
            if (Objects.isNull(changeStatus.getOldValue()) || Double.parseDouble(changeStatus.getOldValue()) > 3.5) {
                VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);
                if (VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
                    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeInfoRepository.get(vehicleName);
                    Double currentStopFinishedMileage = vehicleRealtimeInfoDTO.getCurrentStopFinishedMileage();
                    if (currentStopFinishedMileage != null && currentStopFinishedMileage <= DISTANCE_THRESHOLD) {
                        changeStatus.setNewValue(String.valueOf(sceneSignal));
                        sceneSignalMap.put(vehicleName, changeStatus, ExpirationPolicy.CREATED, 5, TimeUnit.SECONDS);
                    }
                }
            }
        } else if (sceneSignal > 3.5) {
            //车辆连续10s定位置信度大于3.5消失
            if (Objects.isNull(changeStatus.getOldValue()) || vehicleSceneSignalMap.getOrDefault(vehicleName,  Double.parseDouble(changeStatus.getOldValue())) <= 3.5) {
                changeStatus.setNewValue(String.valueOf(sceneSignal));
                sceneSignalMap.put(vehicleName, changeStatus, ExpirationPolicy.CREATED, 10, TimeUnit.SECONDS);
            }
        }
        vehicleSceneSignalMap.put(vehicleName, sceneSignal);
    }

    /**
     * 处理行驶过程中车辆重启发车时出现的定位异常
     *
     * @param vehicleRealtimeInfoDTO vehicleRealtimeInfoDTO
     */
    public void handleLocalizationError(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO, Map<String, VehicleAlarmEventDTO> alarmMap) {
        // 车辆当前定位置信度在区间内[0,3.5]
        String vehicleName = vehicleRealtimeInfoDTO.getVehicleName();
        Double sceneSignal = vehicleSceneSignalMap.get(vehicleName);
        if (sceneSignal == null || sceneSignal < 0 || sceneSignal > 3.5) {
            return;
        }

        // 判断当前是否已经生成定位异常告警
        if (!Objects.isNull(alarmMap) && !Objects.isNull(alarmMap.get(AlarmTypeEnum.LOCALIZATION_ERROR.getValue()))) {
            return;
        }

        // 车辆当前规划路线行驶里程在100米内
        Double currentStopFinishedMileage = vehicleRealtimeInfoDTO.getCurrentStopFinishedMileage();
        if (currentStopFinishedMileage == null || currentStopFinishedMileage > DISTANCE_THRESHOLD) {
            return;
        }

        // 车辆有Planning
        VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);
        if (!VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
            return;
        }

        // 车辆连续5s定位置信度小于3.5，上报
        ChangeStatusDTO changeStatus = new ChangeStatusDTO();
        changeStatus.setVehicleName(vehicleName);
        changeStatus.setNewValue(String.valueOf(sceneSignal));
        sceneSignalMap.put(vehicleName, changeStatus, ExpirationPolicy.CREATED, 5, TimeUnit.SECONDS);
        log.info("车辆任务中重启出现定位异常，{}，{}", vehicleName, sceneSignal);
    }

    /**
     * 时间持续监听器
     */
    class ExpirationDataListener implements ExpirationListener<String, ChangeStatusDTO> {
        @Override
        public void expired(String vehicleName, ChangeStatusDTO value) {
            double sceneSignal = Double.parseDouble(value.getNewValue());
            Map<String, VehicleAlarmEventDTO> alarmMap = reportAlarmRepository.get(vehicleName);
            if (sceneSignal >= 0 && sceneSignal <= 3.5) {
                if (Objects.isNull(alarmMap) || Objects.isNull(alarmMap.get(AlarmTypeEnum.LOCALIZATION_ERROR.getValue()))) {
                    vehicleAlarmManager.addAlarm(vehicleName, AlarmTypeEnum.LOCALIZATION_ERROR.getValue(), new Date(), alarmMap);
                }
            } else if (sceneSignal > 3.5) {
                if (!Objects.isNull(alarmMap) && !Objects.isNull(alarmMap.get(AlarmTypeEnum.LOCALIZATION_ERROR.getValue()))) {
                    vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.LOCALIZATION_ERROR.getValue());
                }
            }
        }
    }
}
