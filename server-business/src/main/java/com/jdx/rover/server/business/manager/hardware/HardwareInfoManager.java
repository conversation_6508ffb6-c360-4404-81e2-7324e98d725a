/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.hardware;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.info.BaseDeviceInfo;
import com.jdx.rover.hardware.info.CameraInfo;
import com.jdx.rover.hardware.info.HardwareInfo;
import com.jdx.rover.hardware.info.LidarInfo;
import com.jdx.rover.hardware.info.MessageType;
import com.jdx.rover.hardware.info.SimInfo;
import com.jdx.rover.server.api.domain.dto.hardware.info.BaseDeviceInfoDTO;
import com.jdx.rover.server.api.domain.dto.hardware.info.CameraInfoDTO;
import com.jdx.rover.server.api.domain.dto.hardware.info.HardwareInfoDTO;
import com.jdx.rover.server.api.domain.dto.hardware.info.LidarInfoDTO;
import com.jdx.rover.server.api.domain.dto.hardware.info.SimInfoDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.convert.ConvertListUtil;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.hardware.HardwareInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * 车辆硬件信息数据管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareInfoManager {
    private final HardwareInfoRepository hardwareInfoRepository;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;
    /**
     * 处理硬件信息
     *
     * @param hardwareInfo 硬件信息
     */
    public void processHardwareInfo(HardwareInfo hardwareInfo) {
        try {
            HardwareInfoDTO dto = convertToDTO(hardwareInfo);
            hardwareInfoRepository.set(dto);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareInfo(), JsonUtils.writeValueAsString(dto), dto.getVehicleName());
        } catch (Exception e) {
            log.error("处理硬件信息数据失败: {}", hardwareInfo.getHeader().getSendName(), e);
        }
    }

    /**
     * 将Protobuf对象转换为DTO对象
     *
     * @param hardwareInfo Protobuf硬件信息对象
     * @return 硬件信息DTO对象
     */
    private HardwareInfoDTO convertToDTO(HardwareInfo hardwareInfo) {
        String vehicleName = hardwareInfo.getHeader().getSendName();
        Date recordTime = new Date(hardwareInfo.getHeader().getRequestTime());

        // 获取现有数据或创建新对象
        HardwareInfoDTO dto = Optional.ofNullable(hardwareInfoRepository.get(vehicleName))
                .orElse(new HardwareInfoDTO());

        // 设置基本信息
        dto.setVehicleName(vehicleName);
        dto.setRecordTime(recordTime);

        // 根据消息类型更新对应字段
        for (MessageType messageType : hardwareInfo.getMessageTypeList()) {
            updateDTOByMessageType(dto, hardwareInfo, messageType, recordTime);
        }

        return dto;
    }

    /**
     * 根据消息类型更新DTO对象的相应字段
     *
     * @param dto          硬件信息DTO对象
     * @param hardwareInfo Protobuf硬件信息对象
     * @param messageType  消息类型
     * @param recordTime   记录时间
     */
    private void updateDTOByMessageType(HardwareInfoDTO dto, HardwareInfo hardwareInfo,
                                        MessageType messageType, Date recordTime) {
        switch (messageType) {
            case ROUTER_INFO -> {
                if (!hardwareInfo.getRouterInfoList().isEmpty()) {
                    dto.setRouterInfo(convertBaseDeviceInfo(hardwareInfo.getRouterInfo(0), recordTime));
                }
            }
            case SIM_INFO -> dto.setSimInfoList(ConvertListUtil.convertList(hardwareInfo.getSimInfoList(),
                    item -> convertSimInfo(item, recordTime)));
            case GNSS_INFO -> {
                if (!hardwareInfo.getGnssInfoList().isEmpty()) {
                    dto.setGnssInfo(convertBaseDeviceInfo(hardwareInfo.getGnssInfo(0), recordTime));
                }
            }
            case DOMAIN_INFO -> dto.setDomainInfoList(ConvertListUtil.convertList(hardwareInfo.getDomainInfoList(),
                    item -> convertBaseDeviceInfo(item, recordTime)));
            case LIDAR_INFO -> dto.setLidarInfoList(ConvertListUtil.convertList(hardwareInfo.getLidarInfoList(),
                    item -> convertLidarInfo(item, recordTime)));
            case CAMERA_INFO -> dto.setCameraInfoList(ConvertListUtil.convertList(hardwareInfo.getCameraInfoList(),
                    item -> convertCameraInfo(item, recordTime)));
            case CHASSIS_INFO -> {
                if (!hardwareInfo.getChassisInfoList().isEmpty()) {
                    dto.setChassisInfo(convertBaseDeviceInfo(hardwareInfo.getChassisInfo(0), recordTime));
                }
            }
            case ANDROID_INFO -> {
                if (!hardwareInfo.getAndroidInfoList().isEmpty()) {
                    dto.setAndroidInfo(convertBaseDeviceInfo(hardwareInfo.getAndroidInfo(0), recordTime));
                }
            }
            default -> {
            }
        }
    }

    /**
     * 转换基础设备信息
     *
     * @param baseDeviceInfo 基础设备信息
     * @param recordTime     记录时间
     * @return 基础设备信息DTO
     */
    private BaseDeviceInfoDTO convertBaseDeviceInfo(BaseDeviceInfo baseDeviceInfo, Date recordTime) {
        if (baseDeviceInfo == null) {
            return null;
        }

        BaseDeviceInfoDTO dto = new BaseDeviceInfoDTO();
        dto.setType(baseDeviceInfo.getType());
        dto.setSn(baseDeviceInfo.getSn());
        dto.setVersion(baseDeviceInfo.getVersion());
        dto.setDeviceId(baseDeviceInfo.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }

    /**
     * 转换SIM卡信息
     *
     * @param simInfo    SIM卡信息
     * @param recordTime 记录时间
     * @return SIM卡信息DTO
     */
    private SimInfoDTO convertSimInfo(SimInfo simInfo, Date recordTime) {
        if (simInfo == null) {
            return null;
        }

        SimInfoDTO dto = new SimInfoDTO();
        dto.setOperator(simInfo.getOperator().getNumber());
        dto.setImsi(simInfo.getImsi());
        dto.setModuleInfo(simInfo.getModuleInfo());
        dto.setIccid(simInfo.getIccid());
        dto.setPdu(simInfo.getPdu());
        dto.setDeviceId(simInfo.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }

    /**
     * 转换激光雷达信息
     *
     * @param lidarInfo  激光雷达信息
     * @param recordTime 记录时间
     * @return 激光雷达信息DTO
     */
    private LidarInfoDTO convertLidarInfo(LidarInfo lidarInfo, Date recordTime) {
        if (lidarInfo == null) {
            return null;
        }

        LidarInfoDTO dto = new LidarInfoDTO();
        dto.setPn(lidarInfo.getPn());
        dto.setSn(lidarInfo.getSn());
        dto.setSoftVersion(lidarInfo.getSoftVersion());
        dto.setHardwareVersion(lidarInfo.getHardwareVersion());
        dto.setPosition(lidarInfo.getPosition());
        dto.setType(lidarInfo.getType());
        dto.setDeviceId(lidarInfo.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }

    /**
     * 转换摄像头信息
     *
     * @param cameraInfo 摄像头信息
     * @param recordTime 记录时间
     * @return 摄像头信息DTO
     */
    private CameraInfoDTO convertCameraInfo(CameraInfo cameraInfo, Date recordTime) {
        if (cameraInfo == null) {
            return null;
        }

        CameraInfoDTO dto = new CameraInfoDTO();
        dto.setVersion(cameraInfo.getVersion());
        dto.setSn(cameraInfo.getSn());
        dto.setPosition(cameraInfo.getPosition());
        dto.setType(cameraInfo.getType());
        dto.setDeviceId(cameraInfo.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }
}