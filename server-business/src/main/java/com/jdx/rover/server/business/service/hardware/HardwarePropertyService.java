/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.service.hardware;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.MessageState;
import com.jdx.rover.hardware.info.HardwareInfo;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttToJmqDTO;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttQosEnum;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.hardware.HardwareInfoManager;
import com.jdx.rover.server.business.manager.hardware.HardwareStatusManager;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 车辆硬件属性数据服务,负责接收和分发硬件信息数据到相应的管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwarePropertyService {
    /**
     * mqtt响应主题前缀
     */
    private static final String MQTT_PROPERTY_REPLY = "r/hardware/property_reply/";

    private final HardwareInfoManager hardwareInfoManager;
    private final HardwareStatusManager hardwareStatusManager;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 接收并处理JMQ消息
     *
     * @param message JMQ消息
     */
    public void receive(Message message) {
        try {
            MqttToJmqDTO<byte[]> jmqTopicDTO = JsonUtils.readByteValue(message.getByteBody(), new TypeReference<>() {
            });
            if (Objects.isNull(jmqTopicDTO) || Objects.isNull(jmqTopicDTO.getData())) {
                log.warn("接收到空的JMQ消息");
                return;
            }
            HardwareInfo hardwareInfo = HardwareInfo.parseFrom(jmqTopicDTO.getData());
            sendResponseMqtt(hardwareInfo);

            hardwareInfoManager.processHardwareInfo(hardwareInfo);
            hardwareStatusManager.processHardwareStatus(hardwareInfo);

            log.info("接收到硬件信息数据: {}", ProtoUtils.protoToJson(hardwareInfo));
        } catch (Exception e) {
            log.error("解析硬件信息数据失败: {}", message, e);
        }
    }

    /**
     * 发送MQTT响应消息
     */
    private void sendResponseMqtt(HardwareInfo hardwareInfo) {
        String vehicleName = hardwareInfo.getHeader().getSendName();
        if (StringUtils.isBlank(vehicleName)) {
            log.warn("无法发送MQTT响应：车辆名称为空");
            return;
        }
        try {
            // 构建响应消息
            HardwareInfo response = buildResponseMessage(hardwareInfo);

            // 构建MQTT消息
            MqttMessageVO<byte[]> mqttMessageVO = buildMqttMessage(response, vehicleName);

            // 发送消息
            sendMqttMessageToJmq(mqttMessageVO, vehicleName);

            log.debug("成功发送MQTT响应消息: {}", vehicleName);
        } catch (Exception e) {
            log.error("发送MQTT响应消息失败: {}", vehicleName, e);
        }
    }

    /**
     * 构建响应消息
     *
     * @param originalMessage 原始消息
     * @return 构建的响应消息
     */
    private HardwareInfo buildResponseMessage(HardwareInfo originalMessage) {
        HardwareInfo.Builder responseBuilder = HardwareInfo.newBuilder();

        // 复制原始消息头并设置响应状态
        responseBuilder.setHeader(originalMessage.getHeader());
        responseBuilder.getHeaderBuilder()
                .setMessageState(MessageState.SUCCESS)
                .setResponseTime(System.currentTimeMillis());
        return responseBuilder.build();
    }

    /**
     * 构建MQTT消息
     *
     * @param response    响应消息
     * @param vehicleName 车辆名称
     * @return MQTT消息对象
     */
    private MqttMessageVO<byte[]> buildMqttMessage(HardwareInfo response, String vehicleName) {
        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();

        // 设置主题和消息体
        String responseTopic = MQTT_PROPERTY_REPLY + vehicleName;
        mqttMessageVO.setTopic(responseTopic);
        mqttMessageVO.setMessage(response.toByteArray());

        // 添加额外的消息属性（如果需要）
        mqttMessageVO.setQos(MqttQosEnum.AT_LEAST_ONCE.getValue()); // 设置QoS级别确保消息送达
        mqttMessageVO.setRetained(false);

        return mqttMessageVO;
    }

    /**
     * 将MQTT消息发送到JMQ
     *
     * @param mqttMessageVO MQTT消息对象
     * @param vehicleName   车辆名称（用作业务ID）
     * @throws JsonProcessingException JSON序列化异常
     */
    private void sendMqttMessageToJmq(MqttMessageVO<byte[]> mqttMessageVO, String vehicleName) throws JsonProcessingException {
        // 准备JMQ消息
        Message message = new Message();
        message.setTopic(producerTopicProperties.getMqttSendByte());
        message.setBody(JsonUtils.getObjectMapper().writeValueAsBytes(mqttMessageVO));
        message.setBusinessId(vehicleName);

        jmqProducerManager.sendOrdered(message);
    }
}