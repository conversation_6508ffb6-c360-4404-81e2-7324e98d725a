spring:
  application:
    name: rover-server-business
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
    time-zone: GMT+8
  kafka:
    producer:
      retries: 5
    consumer:
      group-id: ${spring.application.name}
    listener:
      type: batch
      concurrency: 3
  data:
    redis:
      password: root
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=5000,expireAfterWrite=600s"
logging:
  config: classpath:log4j2.xml

jdd:
  easyjob:
    enable: true
    appId: rover-server-business

project:
  kafka:
    second:
      bootstrap-servers: *************:9092
  local:
    cache:
      localCacheEvictTopic: server:local:cache:evict

#jsf
jsf:
  provider:
    validation: true

jmq:
  address: test-nameserver.jmq.jd.local:50088
  password: 3f9e1de79acd4e3ba7e1e1aec51bf274
  app: roverServer
  topic:
    suffix: _${spring.profiles.active}
    producer:
      serverGuardianPnc: server_guardian_pnc${jmq.topic.suffix}
      serverVehicleChangeStatus: server_vehicle_change_status${jmq.topic.suffix}
      serverGuardianAbnormal: server_guardian_abnormal${jmq.topic.suffix}
      serverGuardianPower: server_guardian_power${jmq.topic.suffix}
      serverGuardianRealtime: server_guardian_realtime${jmq.topic.suffix}
      serverGuardianOtaVersion: server_guardian_ota_version${jmq.topic.suffix}
      serverReportAbnormal: server_report_abnormal${jmq.topic.suffix}
      serverGuardianAlarm: server_guardian_alarm${jmq.topic.suffix}
      serverReportBoot: server_report_boot${jmq.topic.suffix}
      serverReportDriveMode: server_report_drive_mode${jmq.topic.suffix}
      serverReportRoutingStatus: server_report_routing_status${jmq.topic.suffix}
      serverReportHardwareEvent: server_report_hardware_event${jmq.topic.suffix}
      serverReportRoutingAll: server_report_routing_all${jmq.topic.suffix}
      serverBusObu: server_bus_obu${jmq.topic.suffix}
      serverDecreaseLocalView: server_decrease_local_view${jmq.topic.suffix}
      serverHardwareInfo: server_hardware_info${jmq.topic.suffix}
      serverHardwareStatus: server_hardware_status${jmq.topic.suffix}
      serverHardwareSimData: server_hardware_sim_data${jmq.topic.suffix}
      serverHardwareGnssData: server_hardware_gnss_data${jmq.topic.suffix}
      serverHardwareDomainData: server_hardware_domain_data${jmq.topic.suffix}
      serverHardwareChassisData: server_hardware_chassis_data${jmq.topic.suffix}
      serverHardwareConnectivity: server_hardware_connectivity${jmq.topic.suffix}
      mqttSendByte: mqtt_send_byte${jmq.topic.suffix}
    consumer:
      server_web_terminal_command_down: server_web_terminal_command_down${jmq.topic.suffix}
      server_vehicle_change_status: server_vehicle_change_status${jmq.topic.suffix}
      server_report_origin: server_report_origin${jmq.topic.suffix}
      map_module_version: map_module_version${jmq.topic.suffix}
      server_local_view: server_local_view${jmq.topic.suffix}
      server_upstream_guardian_origin: server_upstream_guardian_origin${jmq.topic.suffix}
      server_bus_origin: server_bus_origin${jmq.topic.suffix}
      mqtt_android_info: mqtt_android_info${jmq.topic.suffix}
      mqtt_hardware_property: mqtt_hardware_property${jmq.topic.suffix}
      mqtt_hardware_data: mqtt_hardware_data${jmq.topic.suffix}
      mqtt_hardware_services_reply: mqtt_hardware_services_reply${jmq.topic.suffix}