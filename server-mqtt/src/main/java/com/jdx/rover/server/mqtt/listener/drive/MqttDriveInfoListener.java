/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.service.MqttToKafkaService;
import jdx.rover.remote.drive.control.proto.DriveControlDto;
import jdx.rover.remote.drive.vehicle.proto.DriveDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttDriveInfoListener {
    @Autowired
    private MqttToKafkaService mqttToKafkaService;

    /**
     * 平行驾驶驾驶舱指令
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/cockpit/command/#","drive/cockpit/command/+"}, qos = 0
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void cockpitCommand(MqttMessageDTO<byte[]> message) {
        try {
//            mqttToKafkaService.sendByteKafka(message.getTopic(), message.getPayload());
            DriveControlDto.ControlCommand dto = DriveControlDto.ControlCommand.parseFrom(message.getPayload());
            log.info("receive topic={},payload={}", message.getTopic(), ProtoUtils.protoToJson(dto));
        } catch (Exception e) {
            log.error("hand drive cockpit command info error topic={},payload={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }

    /**
     * 平行驾驶车端状态信息群发
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/vehicle/group/info/#","drive/vehicle/data/+"}, qos = 0
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void vehicleInfo(MqttMessageDTO<byte[]> message) {
        try {
//            mqttToKafkaService.sendByteKafka(message.getTopic(), message.getPayload());
            DriveDataDto.DriveDataDTO dto = DriveDataDto.DriveDataDTO.parseFrom(message.getPayload());
            log.info("receive topic={},payload={}", message.getTopic(), ProtoUtils.protoToJson(dto));
        } catch (Exception e) {
            log.error("hand drive vehicle info error topic={}, payload={}, error={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }
}
