/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 大屏数据上传
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttScreenDataListener {
    /**
     * 大屏数据上传
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/screen/data/#","r/+/drive/screen_data/+"}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void screenData(MqttMessageDTO<String> message) {
        log.info("收到大屏mqtt上传消息,topic={},payload={}", message.getTopic(), message.getPayload());
    }
}
