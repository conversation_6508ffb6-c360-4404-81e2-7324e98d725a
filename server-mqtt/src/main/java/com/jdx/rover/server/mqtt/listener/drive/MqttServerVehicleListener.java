/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import jdx.rover.remote.drive.server.proto.DriveServerVehicle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttServerVehicleListener {
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 远程遥控连接消息
     *
     * @param message
     */
    @MqttListener(topics = {"reply/r/drive/server/vehicle/+/+", "drive/server/vehicle_reply/+"}
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT, MqttBroker.VEHICLE_MQTT})
    public void serverVehicle(MqttMessageDTO<byte[]> message) {
        try {
            DriveServerVehicle.ServerToVehicle dto = DriveServerVehicle.ServerToVehicle.parseFrom(message.getPayload());
            String businessId = dto.getResponseHeader().getClientName();
            String jsonStr = ProtoUtils.protoToJson(dto);
            jmqProducerManager.sendOrdered(producerTopicProperties.getDriveServerVehicleReply(), jsonStr, businessId);
            log.info("receive topic={},payload={}", message.getTopic(), jsonStr);
        } catch (Exception e) {
            log.error("hand drive server to vehicle info error topic={},payload={}, error={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }
}
